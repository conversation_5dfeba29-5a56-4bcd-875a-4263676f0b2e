#!/usr/bin/env python3
"""
Script para gerar gráficos rápidos das principais ações brasileiras
"""

import yfinance as yf
import matplotlib.pyplot as plt
import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Configurar matplotlib
plt.style.use('default')
plt.rcParams['figure.figsize'] = (14, 8)
plt.rcParams['font.size'] = 10

# Principais ações brasileiras (mais líquidas)
PRINCIPAIS_ACOES = {
    'PETR4.SA': 'Petrobras',
    'VALE3.SA': 'Vale',
    'ITUB4.SA': 'Itaú Unibanco',
    'BBDC4.SA': 'Bradesco',
    'ABEV3.SA': 'Ambev',
    'WEGE3.SA': 'WEG',
    'RENT3.SA': 'Localiza',
    'LREN3.SA': 'Lojas Renner',
    'MGLU3.SA': 'Magazine Luiza',
    'SUZB3.SA': '<PERSON><PERSON>',
    'JBSS3.SA': 'JBS',
    'B3SA3.SA': 'B3',
    'RAIL3.SA': 'Rumo',
    'CCRO3.SA': 'CCR',
    'ELET3.SA': 'Eletrobras'
}

def obter_dados_rapido(ticker, periodo="1y"):
    """Obtém dados de forma rápida com tratamento de erro"""
    try:
        stock = yf.Ticker(ticker)
        dados = stock.history(period=periodo)
        return dados if not dados.empty else None
    except:
        return None

def criar_grafico_multiplo(acoes_dict, titulo="Ações Brasileiras - Último Ano"):
    """
    Cria um gráfico com múltiplas ações normalizadas (base 100)
    """
    fig, ax = plt.subplots(figsize=(15, 10))
    
    cores = plt.cm.tab20(np.linspace(0, 1, len(acoes_dict)))
    dados_validos = {}
    
    print("📊 Obtendo dados das ações...")
    for i, (ticker, nome) in enumerate(acoes_dict.items()):
        print(f"  • {ticker.replace('.SA', '')}: {nome}")
        dados = obter_dados_rapido(ticker)
        
        if dados is not None:
            # Normalizar para base 100
            performance = (dados['Close'] / dados['Close'].iloc[0]) * 100
            ax.plot(performance.index, performance, 
                   linewidth=2.5, label=f"{ticker.replace('.SA', '')} - {nome}", 
                   color=cores[i])
            dados_validos[ticker] = dados
        else:
            print(f"    ⚠️ Sem dados para {ticker}")
    
    # Configurar gráfico
    ax.set_title(titulo, fontsize=16, fontweight='bold', pad=20)
    ax.set_xlabel('Data', fontsize=12)
    ax.set_ylabel('Performance Relativa (Base 100)', fontsize=12)
    ax.grid(True, alpha=0.3)
    ax.axhline(y=100, color='black', linestyle='--', alpha=0.7, linewidth=1)
    
    # Legenda
    ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=9)
    
    # Adicionar estatísticas
    if dados_validos:
        performances_finais = []
        for ticker, dados in dados_validos.items():
            perf = ((dados['Close'].iloc[-1] / dados['Close'].iloc[0]) - 1) * 100
            performances_finais.append(perf)
        
        media_perf = np.mean(performances_finais)
        melhor_perf = max(performances_finais)
        pior_perf = min(performances_finais)
        
        stats_text = f"Média: {media_perf:.1f}% | Melhor: {melhor_perf:.1f}% | Pior: {pior_perf:.1f}%"
        ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, 
                fontsize=10, verticalalignment='top',
                bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
    
    plt.tight_layout()
    return fig, dados_validos

def criar_grafico_setores():
    """Cria gráfico comparativo por setores"""
    setores = {
        'Bancos': {'ITUB4.SA': 'Itaú', 'BBDC4.SA': 'Bradesco', 'SANB11.SA': 'Santander'},
        'Commodities': {'PETR4.SA': 'Petrobras', 'VALE3.SA': 'Vale', 'SUZB3.SA': 'Suzano'},
        'Consumo': {'ABEV3.SA': 'Ambev', 'LREN3.SA': 'Renner', 'MGLU3.SA': 'Magazine Luiza'},
        'Infraestrutura': {'CCRO3.SA': 'CCR', 'RAIL3.SA': 'Rumo', 'ELET3.SA': 'Eletrobras'}
    }
    
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    axes = axes.flatten()
    
    for i, (setor, acoes) in enumerate(setores.items()):
        ax = axes[i]
        cores = plt.cm.Set1(np.linspace(0, 1, len(acoes)))
        
        for j, (ticker, nome) in enumerate(acoes.items()):
            dados = obter_dados_rapido(ticker)
            if dados is not None:
                performance = (dados['Close'] / dados['Close'].iloc[0]) * 100
                ax.plot(performance.index, performance, 
                       linewidth=2, label=f"{ticker.replace('.SA', '')}", 
                       color=cores[j])
        
        ax.set_title(f'Setor: {setor}', fontsize=14, fontweight='bold')
        ax.set_ylabel('Performance (Base 100)')
        ax.grid(True, alpha=0.3)
        ax.axhline(y=100, color='black', linestyle='--', alpha=0.5)
        ax.legend()
    
    plt.suptitle('Comparação por Setores - Último Ano', fontsize=16, fontweight='bold')
    plt.tight_layout()
    return fig

def exibir_resumo_performance(dados_dict):
    """Exibe resumo de performance das ações"""
    print("\n" + "="*70)
    print("📈 RESUMO DE PERFORMANCE - ÚLTIMO ANO")
    print("="*70)
    
    performances = []
    for ticker, dados in dados_dict.items():
        if dados is not None:
            nome = PRINCIPAIS_ACOES.get(ticker, ticker.replace('.SA', ''))
            preco_inicial = dados['Close'].iloc[0]
            preco_final = dados['Close'].iloc[-1]
            performance = ((preco_final / preco_inicial) - 1) * 100
            
            performances.append({
                'ticker': ticker.replace('.SA', ''),
                'nome': nome,
                'performance': performance,
                'preco_atual': preco_final
            })
    
    # Ordenar por performance
    performances.sort(key=lambda x: x['performance'], reverse=True)
    
    print(f"{'Rank':<4} {'Ticker':<8} {'Empresa':<20} {'Performance':<12} {'Preço Atual':<12}")
    print("-" * 70)
    
    for i, acao in enumerate(performances, 1):
        emoji = "🟢" if acao['performance'] >= 0 else "🔴"
        print(f"{i:<4} {emoji} {acao['ticker']:<6} {acao['nome']:<20} "
              f"{acao['performance']:>+8.1f}% {acao['preco_atual']:>10.2f}")

def main():
    print("🚀 GRÁFICOS RÁPIDOS - AÇÕES BRASILEIRAS")
    print("="*50)
    
    print("\nOpções:")
    print("1. Gráfico das principais ações (15 ações)")
    print("2. Gráfico por setores")
    print("3. Ambos + resumo de performance")
    
    opcao = input("\nEscolha uma opção (1-3): ").strip()
    
    if opcao == "1":
        print("\n📊 Criando gráfico das principais ações...")
        fig, dados_dict = criar_grafico_multiplo(PRINCIPAIS_ACOES)
        plt.show()
        exibir_resumo_performance(dados_dict)
        
    elif opcao == "2":
        print("\n📊 Criando gráficos por setores...")
        fig = criar_grafico_setores()
        plt.show()
        
    elif opcao == "3":
        print("\n📊 Análise completa...")
        
        # Gráfico principal
        fig1, dados_dict = criar_grafico_multiplo(PRINCIPAIS_ACOES)
        plt.show()
        
        # Gráfico por setores
        fig2 = criar_grafico_setores()
        plt.show()
        
        # Resumo
        exibir_resumo_performance(dados_dict)
        
    else:
        print("❌ Opção inválida!")
        return
    
    print("\n✅ Análise concluída!")

if __name__ == "__main__":
    main()
