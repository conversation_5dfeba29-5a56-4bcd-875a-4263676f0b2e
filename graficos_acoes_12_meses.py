#!/usr/bin/env python3
"""
Script para gerar gráficos das ações brasileiras - 12 meses
Garante que os gráficos sejam exibidos e salvos
"""

import yfinance as yf
import matplotlib.pyplot as plt
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
import os
warnings.filterwarnings('ignore')

# Configurar matplotlib para garantir exibição
import matplotlib
matplotlib.use('TkAgg')  # Backend para exibir gráficos
plt.ion()  # Modo interativo

# Configurações de estilo
plt.style.use('default')
plt.rcParams['figure.figsize'] = (14, 8)
plt.rcParams['font.size'] = 10
plt.rcParams['axes.grid'] = True

# Ações brasileiras principais (mais líquidas e importantes)
ACOES_PRINCIPAIS = {
    'PETR4.SA': 'Petrobras PN',
    'PETR3.SA': 'Petrobras ON', 
    'VALE3.SA': 'Vale ON',
    'ITUB4.SA': 'Itaú Unibanco PN',
    'BBDC4.SA': 'Bradesco PN',
    'BBDC3.SA': 'Bradesco ON',
    'ABEV3.SA': 'Ambev ON',
    'WEGE3.SA': 'WEG ON',
    'RENT3.SA': 'Localiza ON',
    'LREN3.SA': 'Lojas Renner ON',
    'MGLU3.SA': 'Magazine Luiza ON',
    'SUZB3.SA': 'Suzano ON',
    'JBSS3.SA': 'JBS ON',
    'B3SA3.SA': 'B3 ON',
    'RAIL3.SA': 'Rumo ON',
    'CCRO3.SA': 'CCR ON',
    'ELET3.SA': 'Eletrobras ON',
    'BBAS3.SA': 'Banco do Brasil ON',
    'SANB11.SA': 'Santander Units',
    'RADL3.SA': 'Raia Drogasil ON'
}

def obter_dados_12_meses(ticker):
    """
    Obtém dados dos últimos 12 meses de uma ação
    """
    try:
        print(f"  Obtendo dados de {ticker.replace('.SA', '')}...")
        stock = yf.Ticker(ticker)
        
        # Obter dados dos últimos 12 meses
        dados = stock.history(period="1y", interval="1d")
        
        if dados.empty:
            print(f"    ⚠️ Sem dados para {ticker}")
            return None
            
        print(f"    ✅ {len(dados)} dias de dados obtidos")
        return dados
        
    except Exception as e:
        print(f"    ❌ Erro ao obter {ticker}: {str(e)}")
        return None

def criar_grafico_individual(ticker, nome, dados, salvar=True):
    """
    Cria gráfico individual de uma ação com preço e volume
    """
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10))
    
    # Gráfico de preço
    ax1.plot(dados.index, dados['Close'], linewidth=2, color='#2E86AB', label='Preço de Fechamento')
    ax1.fill_between(dados.index, dados['Low'], dados['High'], alpha=0.2, color='#A23B72', label='Faixa Min-Max')
    
    # Calcular e exibir estatísticas
    preco_inicial = dados['Close'].iloc[0]
    preco_final = dados['Close'].iloc[-1]
    preco_max = dados['Close'].max()
    preco_min = dados['Close'].min()
    performance = ((preco_final / preco_inicial) - 1) * 100
    
    ax1.set_title(f'{nome} ({ticker.replace(".SA", "")}) - Últimos 12 Meses', 
                  fontsize=16, fontweight='bold', pad=20)
    ax1.set_ylabel('Preço (R$)', fontsize=12)
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # Adicionar estatísticas no gráfico
    stats_text = f'Inicial: R$ {preco_inicial:.2f} | Atual: R$ {preco_final:.2f} | Performance: {performance:+.1f}%\n'
    stats_text += f'Máximo: R$ {preco_max:.2f} | Mínimo: R$ {preco_min:.2f}'
    
    cor_performance = '#2E8B57' if performance >= 0 else '#DC143C'
    ax1.text(0.02, 0.98, stats_text, transform=ax1.transAxes, 
             fontsize=10, verticalalignment='top',
             bbox=dict(boxstyle='round,pad=0.5', facecolor='white', alpha=0.9, edgecolor=cor_performance))
    
    # Gráfico de volume
    ax2.bar(dados.index, dados['Volume']/1e6, alpha=0.7, color='#F18F01', width=1)
    ax2.set_title('Volume de Negociação (Milhões)', fontsize=12, fontweight='bold')
    ax2.set_xlabel('Data', fontsize=12)
    ax2.set_ylabel('Volume (Milhões)', fontsize=12)
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # Salvar gráfico
    if salvar:
        nome_arquivo = f"grafico_{ticker.replace('.SA', '')}.png"
        plt.savefig(nome_arquivo, dpi=300, bbox_inches='tight')
        print(f"    💾 Gráfico salvo como: {nome_arquivo}")
    
    plt.show()
    plt.pause(2)  # Pausa para garantir exibição
    
    return fig

def criar_grafico_comparativo_top10(dados_dict):
    """
    Cria gráfico comparativo das top 10 ações (performance normalizada)
    """
    plt.figure(figsize=(16, 10))
    
    # Calcular performances para ordenar
    performances = []
    for ticker, dados in dados_dict.items():
        if dados is not None:
            preco_inicial = dados['Close'].iloc[0]
            preco_final = dados['Close'].iloc[-1]
            performance = ((preco_final / preco_inicial) - 1) * 100
            performances.append((ticker, performance, dados))
    
    # Ordenar por performance e pegar top 10
    performances.sort(key=lambda x: x[1], reverse=True)
    top_10 = performances[:10]
    
    cores = plt.cm.tab20(np.linspace(0, 1, len(top_10)))
    
    print("\n📊 TOP 10 PERFORMANCES:")
    for i, (ticker, performance, dados) in enumerate(top_10):
        nome = ACOES_PRINCIPAIS.get(ticker, ticker.replace('.SA', ''))
        
        # Normalizar para base 100
        performance_normalizada = (dados['Close'] / dados['Close'].iloc[0]) * 100
        
        plt.plot(performance_normalizada.index, performance_normalizada, 
                linewidth=2.5, label=f"{ticker.replace('.SA', '')} ({performance:+.1f}%)", 
                color=cores[i])
        
        print(f"{i+1:2d}. {ticker.replace('.SA', ''):8s} - {nome:20s} {performance:+7.1f}%")
    
    plt.title('TOP 10 Ações Brasileiras - Performance Normalizada (Base 100)', 
              fontsize=16, fontweight='bold', pad=20)
    plt.xlabel('Data', fontsize=12)
    plt.ylabel('Performance Relativa (Base 100)', fontsize=12)
    plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=9)
    plt.grid(True, alpha=0.3)
    plt.axhline(y=100, color='black', linestyle='--', alpha=0.7, linewidth=1)
    
    # Adicionar estatísticas
    performances_valores = [perf for _, perf, _ in top_10]
    media_perf = np.mean(performances_valores)
    plt.text(0.02, 0.02, f'Performance Média TOP 10: {media_perf:.1f}%', 
             transform=plt.gca().transAxes, fontsize=11, fontweight='bold',
             bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
    
    plt.tight_layout()
    plt.savefig('top10_acoes_brasileiras.png', dpi=300, bbox_inches='tight')
    print(f"\n💾 Gráfico comparativo salvo como: top10_acoes_brasileiras.png")
    
    plt.show()
    plt.pause(3)

def main():
    print("📊 GRÁFICOS DE AÇÕES BRASILEIRAS - 12 MESES")
    print("="*60)
    
    print("\nOpções:")
    print("1. Gráficos individuais das principais ações (20 ações)")
    print("2. Gráfico comparativo TOP 10")
    print("3. Ambos (recomendado)")
    print("4. Escolher ações específicas")
    
    opcao = input("\nEscolha uma opção (1-4): ").strip()
    
    if opcao == "4":
        print("\nAções disponíveis:")
        for i, (ticker, nome) in enumerate(ACOES_PRINCIPAIS.items(), 1):
            print(f"{i:2d}. {ticker.replace('.SA', ''):8s} - {nome}")
        
        entrada = input("\nDigite os números das ações separados por vírgula (ex: 1,2,3): ")
        try:
            indices = [int(x.strip()) - 1 for x in entrada.split(",")]
            acoes_selecionadas = {list(ACOES_PRINCIPAIS.keys())[i]: list(ACOES_PRINCIPAIS.values())[i] 
                                for i in indices if 0 <= i < len(ACOES_PRINCIPAIS)}
        except:
            print("❌ Entrada inválida!")
            return
    else:
        acoes_selecionadas = ACOES_PRINCIPAIS
    
    # Obter dados
    print(f"\n📈 Obtendo dados de {len(acoes_selecionadas)} ações...")
    dados_dict = {}
    
    for ticker, nome in acoes_selecionadas.items():
        dados = obter_dados_12_meses(ticker)
        dados_dict[ticker] = dados
    
    # Filtrar apenas ações com dados válidos
    dados_validos = {ticker: dados for ticker, dados in dados_dict.items() if dados is not None}
    
    if not dados_validos:
        print("❌ Nenhum dado válido obtido!")
        return
    
    print(f"\n✅ Dados obtidos para {len(dados_validos)} ações")
    
    # Gerar gráficos conforme opção escolhida
    if opcao == "1" or opcao == "3":
        print(f"\n📊 Gerando gráficos individuais...")
        for ticker, dados in dados_validos.items():
            nome = acoes_selecionadas[ticker]
            print(f"\n🎯 Criando gráfico: {nome}")
            criar_grafico_individual(ticker, nome, dados)
    
    if opcao == "2" or opcao == "3":
        print(f"\n📊 Gerando gráfico comparativo...")
        criar_grafico_comparativo_top10(dados_validos)
    
    print(f"\n✅ Processo concluído!")
    print(f"📁 Gráficos salvos no diretório atual")
    
    # Listar arquivos gerados
    arquivos_png = [f for f in os.listdir('.') if f.endswith('.png')]
    if arquivos_png:
        print(f"\n📋 Arquivos gerados:")
        for arquivo in sorted(arquivos_png):
            print(f"  • {arquivo}")

if __name__ == "__main__":
    main()
