#!/usr/bin/env python3
"""
Exemplo de uso rápido - Demonstração das funcionalidades
"""

import yfinance as yf
import matplotlib.pyplot as plt
import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def exemplo_simples():
    """Exemplo simples de análise de uma ação"""
    print("📊 EXEMPLO SIMPLES - PETROBRAS (PETR4)")
    print("="*50)
    
    # Obter dados da Petrobras
    ticker = "PETR4.SA"
    stock = yf.Ticker(ticker)
    dados = stock.history(period="1y")
    
    if dados.empty:
        print("❌ Não foi possível obter os dados")
        return
    
    # Calcular métricas básicas
    preco_inicial = dados['Close'].iloc[0]
    preco_atual = dados['Close'].iloc[-1]
    performance = ((preco_atual / preco_inicial) - 1) * 100
    preco_max = dados['Close'].max()
    preco_min = dados['Close'].min()
    
    # Exibir informações
    print(f"💰 Preço inicial (1 ano atrás): R$ {preco_inicial:.2f}")
    print(f"💰 Preço atual: R$ {preco_atual:.2f}")
    print(f"📈 Performance no ano: {performance:+.1f}%")
    print(f"📊 Preço máximo: R$ {preco_max:.2f}")
    print(f"📊 Preço mínimo: R$ {preco_min:.2f}")
    
    # Criar gráfico simples
    plt.figure(figsize=(12, 6))
    plt.plot(dados.index, dados['Close'], linewidth=2, color='blue')
    plt.title('PETROBRAS (PETR4) - Último Ano', fontsize=14, fontweight='bold')
    plt.xlabel('Data')
    plt.ylabel('Preço (R$)')
    plt.grid(True, alpha=0.3)
    
    # Adicionar anotação de performance
    cor = 'green' if performance >= 0 else 'red'
    plt.text(0.02, 0.98, f'Performance: {performance:+.1f}%', 
             transform=plt.gca().transAxes, fontsize=12, fontweight='bold',
             verticalalignment='top', color=cor,
             bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
    
    plt.tight_layout()
    plt.show()

def exemplo_comparacao():
    """Exemplo de comparação entre ações"""
    print("\n📊 EXEMPLO COMPARAÇÃO - BANCOS BRASILEIROS")
    print("="*50)
    
    bancos = {
        'ITUB4.SA': 'Itaú Unibanco',
        'BBDC4.SA': 'Bradesco', 
        'SANB11.SA': 'Santander',
        'BBAS3.SA': 'Banco do Brasil'
    }
    
    plt.figure(figsize=(12, 8))
    cores = ['blue', 'red', 'green', 'orange']
    
    print("Obtendo dados dos bancos...")
    for i, (ticker, nome) in enumerate(bancos.items()):
        try:
            stock = yf.Ticker(ticker)
            dados = stock.history(period="1y")
            
            if not dados.empty:
                # Normalizar para base 100
                performance = (dados['Close'] / dados['Close'].iloc[0]) * 100
                plt.plot(performance.index, performance, 
                        linewidth=2, label=nome, color=cores[i])
                
                # Calcular performance final
                perf_final = performance.iloc[-1] - 100
                print(f"  • {nome}: {perf_final:+.1f}%")
            else:
                print(f"  ⚠️ Sem dados para {nome}")
                
        except Exception as e:
            print(f"  ❌ Erro ao obter {nome}: {e}")
    
    plt.title('Comparação Bancos Brasileiros - Base 100', fontsize=14, fontweight='bold')
    plt.xlabel('Data')
    plt.ylabel('Performance Relativa (Base 100)')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.axhline(y=100, color='black', linestyle='--', alpha=0.5)
    plt.tight_layout()
    plt.show()

def exemplo_top5():
    """Exemplo das top 5 ações por volume"""
    print("\n📊 EXEMPLO TOP 5 - AÇÕES MAIS NEGOCIADAS")
    print("="*50)
    
    top5_acoes = {
        'PETR4.SA': 'Petrobras',
        'VALE3.SA': 'Vale',
        'ITUB4.SA': 'Itaú',
        'BBDC4.SA': 'Bradesco',
        'ABEV3.SA': 'Ambev'
    }
    
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(14, 10))
    
    # Gráfico 1: Performance
    cores = plt.cm.Set1(np.linspace(0, 1, len(top5_acoes)))
    performances_finais = []
    
    print("Analisando TOP 5 ações...")
    for i, (ticker, nome) in enumerate(top5_acoes.items()):
        try:
            stock = yf.Ticker(ticker)
            dados = stock.history(period="1y")
            
            if not dados.empty:
                # Performance normalizada
                performance = (dados['Close'] / dados['Close'].iloc[0]) * 100
                ax1.plot(performance.index, performance, 
                        linewidth=2.5, label=nome, color=cores[i])
                
                perf_final = performance.iloc[-1] - 100
                performances_finais.append((nome, perf_final))
                print(f"  • {nome}: {perf_final:+.1f}%")
                
                # Volume médio (últimos 30 dias)
                volume_medio = dados['Volume'].tail(30).mean()
                ax2.bar(nome, volume_medio/1e6, color=cores[i], alpha=0.7)
                
        except Exception as e:
            print(f"  ❌ Erro ao obter {nome}: {e}")
    
    # Configurar gráficos
    ax1.set_title('Performance das TOP 5 Ações - Base 100', fontsize=14, fontweight='bold')
    ax1.set_ylabel('Performance Relativa')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    ax1.axhline(y=100, color='black', linestyle='--', alpha=0.5)
    
    ax2.set_title('Volume Médio de Negociação (Últimos 30 dias)', fontsize=14, fontweight='bold')
    ax2.set_ylabel('Volume (Milhões)')
    ax2.tick_params(axis='x', rotation=45)
    
    plt.tight_layout()
    plt.show()
    
    # Ranking de performance
    if performances_finais:
        print("\n🏆 RANKING DE PERFORMANCE:")
        performances_finais.sort(key=lambda x: x[1], reverse=True)
        for i, (nome, perf) in enumerate(performances_finais, 1):
            emoji = "🟢" if perf >= 0 else "🔴"
            print(f"{i}. {emoji} {nome}: {perf:+.1f}%")

def main():
    print("🚀 EXEMPLOS DE USO - ANÁLISE DE AÇÕES BRASILEIRAS")
    print("="*60)
    
    print("\nEste script demonstra as funcionalidades básicas:")
    print("1. Análise individual de uma ação")
    print("2. Comparação entre ações do mesmo setor")
    print("3. Análise das principais ações por volume")
    
    input("\nPressione Enter para começar...")
    
    # Executar exemplos
    exemplo_simples()
    
    input("\nPressione Enter para continuar com a comparação...")
    exemplo_comparacao()
    
    input("\nPressione Enter para ver o TOP 5...")
    exemplo_top5()
    
    print("\n✅ Exemplos concluídos!")
    print("\nPara análises mais avançadas, use:")
    print("  • uv run python acoes_brasileiras.py")
    print("  • uv run python graficos_rapidos.py")

if __name__ == "__main__":
    main()
