#!/usr/bin/env python3
"""
Exemplo de análise financeira usando as bibliotecas instaladas
"""

import yfinance as yf
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from datetime import datetime, timedelta

def obter_dados_acao(ticker, periodo="1y"):
    """
    Obtém dados históricos de uma ação
    
    Args:
        ticker (str): <PERSON><PERSON><PERSON><PERSON> da ação (ex: 'AAPL', 'GOOGL')
        periodo (str): Período dos dados ('1d', '5d', '1mo', '3mo', '6mo', '1y', '2y', '5y', '10y', 'ytd', 'max')
    
    Returns:
        pd.DataFrame: Dados históricos da ação
    """
    try:
        stock = yf.Ticker(ticker)
        dados = stock.history(period=periodo)
        return dados
    except Exception as e:
        print(f"Erro ao obter dados para {ticker}: {e}")
        return None

def calcular_metricas_basicas(dados):
    """
    Calcula métricas básicas de uma ação
    
    Args:
        dados (pd.DataFrame): Dados históricos da ação
    
    Returns:
        dict: Dicionário com métricas calculadas
    """
    if dados is None or dados.empty:
        return None
    
    # Calcular retornos diários
    dados['Retorno_Diario'] = dados['Close'].pct_change()
    
    metricas = {
        'preco_atual': dados['Close'].iloc[-1],
        'preco_maximo': dados['Close'].max(),
        'preco_minimo': dados['Close'].min(),
        'retorno_total': (dados['Close'].iloc[-1] / dados['Close'].iloc[0] - 1) * 100,
        'volatilidade_anual': dados['Retorno_Diario'].std() * np.sqrt(252) * 100,
        'retorno_medio_diario': dados['Retorno_Diario'].mean() * 100,
        'volume_medio': dados['Volume'].mean()
    }
    
    return metricas

def criar_grafico_preco(dados, ticker):
    """
    Cria gráfico do preço histórico
    
    Args:
        dados (pd.DataFrame): Dados históricos da ação
        ticker (str): Símbolo da ação
    """
    plt.figure(figsize=(12, 6))
    plt.plot(dados.index, dados['Close'], linewidth=2, label='Preço de Fechamento')
    plt.title(f'Preço Histórico - {ticker}', fontsize=16, fontweight='bold')
    plt.xlabel('Data', fontsize=12)
    plt.ylabel('Preço ($)', fontsize=12)
    plt.grid(True, alpha=0.3)
    plt.legend()
    plt.tight_layout()
    plt.show()

def criar_grafico_volume(dados, ticker):
    """
    Cria gráfico do volume de negociação
    
    Args:
        dados (pd.DataFrame): Dados históricos da ação
        ticker (str): Símbolo da ação
    """
    plt.figure(figsize=(12, 4))
    plt.bar(dados.index, dados['Volume'], alpha=0.7, color='orange')
    plt.title(f'Volume de Negociação - {ticker}', fontsize=16, fontweight='bold')
    plt.xlabel('Data', fontsize=12)
    plt.ylabel('Volume', fontsize=12)
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.show()

def analise_completa(ticker, periodo="1y"):
    """
    Realiza análise completa de uma ação
    
    Args:
        ticker (str): Símbolo da ação
        periodo (str): Período dos dados
    """
    print(f"📊 Análise Completa: {ticker}")
    print("=" * 50)
    
    # Obter dados
    dados = obter_dados_acao(ticker, periodo)
    if dados is None:
        print("❌ Não foi possível obter os dados.")
        return
    
    # Calcular métricas
    metricas = calcular_metricas_basicas(dados)
    if metricas is None:
        print("❌ Não foi possível calcular as métricas.")
        return
    
    # Exibir métricas
    print(f"💰 Preço Atual: ${metricas['preco_atual']:.2f}")
    print(f"📈 Preço Máximo: ${metricas['preco_maximo']:.2f}")
    print(f"📉 Preço Mínimo: ${metricas['preco_minimo']:.2f}")
    print(f"📊 Retorno Total: {metricas['retorno_total']:.2f}%")
    print(f"📊 Volatilidade Anual: {metricas['volatilidade_anual']:.2f}%")
    print(f"📊 Retorno Médio Diário: {metricas['retorno_medio_diario']:.4f}%")
    print(f"📊 Volume Médio: {metricas['volume_medio']:,.0f}")
    
    # Criar gráficos
    criar_grafico_preco(dados, ticker)
    criar_grafico_volume(dados, ticker)

def comparar_acoes(tickers, periodo="1y"):
    """
    Compara múltiplas ações
    
    Args:
        tickers (list): Lista de símbolos das ações
        periodo (str): Período dos dados
    """
    print(f"🔄 Comparação de Ações: {', '.join(tickers)}")
    print("=" * 50)
    
    dados_comparacao = {}
    
    for ticker in tickers:
        dados = obter_dados_acao(ticker, periodo)
        if dados is not None:
            # Normalizar preços para comparação (base 100)
            dados_normalizados = (dados['Close'] / dados['Close'].iloc[0]) * 100
            dados_comparacao[ticker] = dados_normalizados
    
    if not dados_comparacao:
        print("❌ Não foi possível obter dados para nenhuma ação.")
        return
    
    # Criar DataFrame para comparação
    df_comparacao = pd.DataFrame(dados_comparacao)
    
    # Gráfico de comparação
    plt.figure(figsize=(12, 8))
    for ticker in df_comparacao.columns:
        plt.plot(df_comparacao.index, df_comparacao[ticker], linewidth=2, label=ticker)
    
    plt.title('Comparação de Performance (Base 100)', fontsize=16, fontweight='bold')
    plt.xlabel('Data', fontsize=12)
    plt.ylabel('Performance Relativa', fontsize=12)
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.show()
    
    # Estatísticas de comparação
    print("\n📊 Estatísticas de Performance:")
    for ticker in df_comparacao.columns:
        performance_final = df_comparacao[ticker].iloc[-1] - 100
        print(f"{ticker}: {performance_final:.2f}%")

if __name__ == "__main__":
    # Exemplo de uso
    print("🚀 Exemplo de Análise Financeira")
    print("=" * 50)
    
    # Análise individual
    analise_completa("AAPL", "6mo")
    
    # Comparação de ações
    comparar_acoes(["AAPL", "GOOGL", "MSFT", "TSLA"], "6mo")
