#!/usr/bin/env python3
"""
Script de teste do ambiente de finanças
"""

import yfinance as yf
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import scipy
from scipy import stats

def main():
    print("🚀 Ambiente de finanças configurado com sucesso!")
    print("📊 Bibliotecas disponíveis:")
    print(f"  - yfinance: {yf.__version__}")
    print(f"  - pandas: {pd.__version__}")
    print(f"  - numpy: {np.__version__}")
    print(f"  - matplotlib: {plt.matplotlib.__version__}")
    print(f"  - seaborn: {sns.__version__}")
    print(f"  - scipy: {scipy.__version__}")

    # Teste básico - buscar dados de uma ação
    print("\n📈 Testando yfinance com dados da Apple (AAPL)...")
    try:
        ticker = yf.Ticker("AAPL")
        info = ticker.info
        print(f"  - Empresa: {info.get('longName', 'N/A')}")
        print(f"  - Setor: {info.get('sector', 'N/A')}")
        print(f"  - Preço atual: ${info.get('currentPrice', 'N/A')}")
        print("✅ yfinance funcionando corretamente!")
    except Exception as e:
        print(f"❌ Erro ao testar yfinance: {e}")

    print("\n🎯 Ambiente pronto para análises financeiras!")


if __name__ == "__main__":
    main()
