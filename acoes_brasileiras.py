#!/usr/bin/env python3
"""
Script para análise de ações brasileiras - Gráficos do último ano
"""

import yfinance as yf
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# Configurar matplotlib para melhor visualização
plt.style.use('seaborn-v0_8')
plt.rcParams['figure.figsize'] = (12, 8)
plt.rcParams['font.size'] = 10

# Dicionário com as ações brasileiras
ACOES_BRASILEIRAS = {
    'ABEV3.SA': 'AMBEV S/A',
    'AZUL4.SA': 'AZUL',
    'B3SA3.SA': 'B3',
    'BBAS3.SA': 'BRASIL',
    'BBDC3.SA': 'BRADESCO',
    'BBDC4.SA': 'BRADESCO',
    'BBSE3.SA': 'BBSEGURIDADE',
    'BEEF3.SA': 'MINERVA',
    'BPAC11.SA': 'BTGP BANCO',
    'BRAP4.SA': 'BRADESPAR',
    'BRDT3.SA': 'PETROBRAS BR',
    'BRFS3.SA': 'BRF SA',
    'BRKM5.SA': 'BRASKEM',
    'BRML3.SA': 'BR MALLS PAR',
    'BTOW3.SA': 'B2W DIGITAL',
    'CCRO3.SA': 'CCR SA',
    'CIEL3.SA': 'CIELO',
    'CMIG4.SA': 'CEMIG',
    'COGN3.SA': 'COGNA ON',
    'CPFE3.SA': 'CPFL ENERGIA',
    'CRFB3.SA': 'CARREFOUR BR',
    'CSAN3.SA': 'COSAN',
    'CSNA3.SA': 'SID NACIONAL',
    'CVCB3.SA': 'CVC BRASIL',
    'CYRE3.SA': 'CYRELA REALT',
    'ECOR3.SA': 'ECORODOVIAS',
    'EGIE3.SA': 'ENGIE BRASIL',
    'ELET3.SA': 'ELETROBRAS',
    'ELET6.SA': 'ELETROBRAS',
    'EMBR3.SA': 'EMBRAER',
    'ENBR3.SA': 'ENERGIAS BR',
    'ENGI11.SA': 'ENERGISA',
    'EQTL3.SA': 'EQUATORIAL',
    'EZTC3.SA': 'EZTEC',
    'FLRY3.SA': 'FLEURY',
    'GGBR4.SA': 'GERDAU',
    'GNDI3.SA': 'INTERMEDICA',
    'GOAU4.SA': 'GERDAU MET',
    'GOLL4.SA': 'GOL',
    'HAPV3.SA': 'HAPVIDA',
    'HGTX3.SA': 'CIA HERING',
    'HYPE3.SA': 'HYPERA',
    'IGTA3.SA': 'IGUATEMI',
    'IRBR3.SA': 'IRBBRASIL RE',
    'ITSA4.SA': 'ITAUSA',
    'ITUB4.SA': 'ITAUUNIBANCO',
    'JBSS3.SA': 'JBS',
    'KLBN11.SA': 'KLABIN S/A',
    'LAME4.SA': 'LOJAS AMERIC',
    'LREN3.SA': 'LOJAS RENNER',
    'MGLU3.SA': 'MAGAZ LUIZA',
    'MRFG3.SA': 'MARFRIG',
    'MRVE3.SA': 'MRV',
    'MULT3.SA': 'MULTIPLAN',
    'NTCO3.SA': 'GRUPO NATURA',
    'PCAR3.SA': 'P.ACUCAR-CBD',
    'PETR3.SA': 'PETROBRAS',
    'PETR4.SA': 'PETROBRAS',
    'PRIO3.SA': 'PETRORIO',
    'QUAL3.SA': 'QUALICORP',
    'RADL3.SA': 'RAIADROGASIL',
    'RAIL3.SA': 'RUMO S.A.',
    'RENT3.SA': 'LOCALIZA',
    'SANB11.SA': 'SANTANDER BR',
    'SBSP3.SA': 'SABESP',
    'SULA11.SA': 'SUL AMERICA',
    'SUZB3.SA': 'SUZANO S.A.',
    'TAEE11.SA': 'TAESA',
    'TIMS3.SA': 'TIM',
    'TOTS3.SA': 'TOTVS',
    'UGPA3.SA': 'ULTRAPAR',
    'USIM5.SA': 'USIMINAS',
    'VALE3.SA': 'VALE',
    'VIVT4.SA': 'TELEF BRASIL',
    'VVAR3.SA': 'VIAVAREJO',
    'WEGE3.SA': 'WEG',
    'YDUQ3.SA': 'YDUQS PART'
}

def obter_dados_acao(ticker, periodo="1y"):
    """
    Obtém dados históricos de uma ação brasileira
    
    Args:
        ticker (str): Código da ação (ex: 'PETR4.SA')
        periodo (str): Período dos dados
    
    Returns:
        pd.DataFrame: Dados históricos ou None se erro
    """
    try:
        stock = yf.Ticker(ticker)
        dados = stock.history(period=periodo)
        if dados.empty:
            print(f"⚠️  Sem dados para {ticker}")
            return None
        return dados
    except Exception as e:
        print(f"❌ Erro ao obter dados para {ticker}: {e}")
        return None

def criar_grafico_individual(ticker, nome_empresa, dados):
    """
    Cria gráfico individual para uma ação
    """
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))
    
    # Gráfico de preço
    ax1.plot(dados.index, dados['Close'], linewidth=2, color='#1f77b4')
    ax1.set_title(f'{nome_empresa} ({ticker}) - Preço de Fechamento (Último Ano)', 
                  fontsize=14, fontweight='bold')
    ax1.set_ylabel('Preço (R$)', fontsize=12)
    ax1.grid(True, alpha=0.3)
    
    # Adicionar informações de performance
    preco_inicial = dados['Close'].iloc[0]
    preco_final = dados['Close'].iloc[-1]
    performance = ((preco_final / preco_inicial) - 1) * 100
    
    cor_performance = 'green' if performance >= 0 else 'red'
    ax1.text(0.02, 0.98, f'Performance: {performance:.1f}%', 
             transform=ax1.transAxes, fontsize=11, fontweight='bold',
             verticalalignment='top', color=cor_performance,
             bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
    
    # Gráfico de volume
    ax2.bar(dados.index, dados['Volume'], alpha=0.7, color='orange')
    ax2.set_title('Volume de Negociação', fontsize=12, fontweight='bold')
    ax2.set_xlabel('Data', fontsize=12)
    ax2.set_ylabel('Volume', fontsize=12)
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    return fig

def criar_grafico_comparativo(acoes_selecionadas, dados_dict):
    """
    Cria gráfico comparativo de múltiplas ações (performance normalizada)
    """
    plt.figure(figsize=(15, 10))
    
    cores = plt.cm.tab20(np.linspace(0, 1, len(acoes_selecionadas)))
    
    for i, ticker in enumerate(acoes_selecionadas):
        if ticker in dados_dict and dados_dict[ticker] is not None:
            dados = dados_dict[ticker]
            # Normalizar para base 100
            performance_normalizada = (dados['Close'] / dados['Close'].iloc[0]) * 100
            
            plt.plot(performance_normalizada.index, performance_normalizada, 
                    linewidth=2, label=f"{ticker.replace('.SA', '')} - {ACOES_BRASILEIRAS[ticker]}", 
                    color=cores[i])
    
    plt.title('Comparação de Performance - Ações Brasileiras (Base 100)', 
              fontsize=16, fontweight='bold')
    plt.xlabel('Data', fontsize=12)
    plt.ylabel('Performance Relativa (Base 100)', fontsize=12)
    plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    plt.grid(True, alpha=0.3)
    plt.axhline(y=100, color='black', linestyle='--', alpha=0.5)
    plt.tight_layout()
    plt.show()

def exibir_ranking_performance(dados_dict):
    """
    Exibe ranking das ações por performance
    """
    performances = []
    
    for ticker, dados in dados_dict.items():
        if dados is not None and not dados.empty:
            preco_inicial = dados['Close'].iloc[0]
            preco_final = dados['Close'].iloc[-1]
            performance = ((preco_final / preco_inicial) - 1) * 100
            performances.append({
                'Ticker': ticker.replace('.SA', ''),
                'Empresa': ACOES_BRASILEIRAS[ticker],
                'Performance (%)': performance,
                'Preço Inicial (R$)': preco_inicial,
                'Preço Final (R$)': preco_final
            })
    
    # Ordenar por performance
    performances.sort(key=lambda x: x['Performance (%)'], reverse=True)
    
    print("\n" + "="*80)
    print("🏆 RANKING DE PERFORMANCE - ÚLTIMO ANO")
    print("="*80)
    
    for i, acao in enumerate(performances[:10], 1):
        emoji = "🟢" if acao['Performance (%)'] >= 0 else "🔴"
        print(f"{i:2d}. {emoji} {acao['Ticker']:8s} | {acao['Empresa']:20s} | "
              f"{acao['Performance (%)']:+7.1f}% | R$ {acao['Preço Final (R$)']:7.2f}")
    
    print("\n" + "="*80)
    print("📉 PIORES PERFORMANCES")
    print("="*80)
    
    for i, acao in enumerate(performances[-10:], 1):
        emoji = "🟢" if acao['Performance (%)'] >= 0 else "🔴"
        print(f"{i:2d}. {emoji} {acao['Ticker']:8s} | {acao['Empresa']:20s} | "
              f"{acao['Performance (%)']:+7.1f}% | R$ {acao['Preço Final (R$)']:7.2f}")

def main():
    print("📊 ANÁLISE DE AÇÕES BRASILEIRAS - ÚLTIMO ANO")
    print("="*60)
    
    print("\nOpções disponíveis:")
    print("1. Gráfico comparativo de ações selecionadas")
    print("2. Gráficos individuais de ações específicas")
    print("3. Ranking de performance de todas as ações")
    print("4. Análise completa (todas as opções)")
    
    opcao = input("\nEscolha uma opção (1-4): ").strip()
    
    if opcao == "1":
        # Gráfico comparativo
        print("\nAções disponíveis para comparação:")
        tickers = list(ACOES_BRASILEIRAS.keys())
        for i, ticker in enumerate(tickers[:20], 1):  # Mostrar primeiras 20
            print(f"{i:2d}. {ticker.replace('.SA', ''):8s} - {ACOES_BRASILEIRAS[ticker]}")
        print("... (e mais ações)")
        
        entrada = input("\nDigite os códigos das ações separados por vírgula (ex: PETR4,VALE3,ITUB4): ")
        acoes_selecionadas = [f"{ticker.strip().upper()}.SA" for ticker in entrada.split(",")]
        
        print(f"\n📈 Obtendo dados para {len(acoes_selecionadas)} ações...")
        dados_dict = {}
        for ticker in acoes_selecionadas:
            if ticker in ACOES_BRASILEIRAS:
                dados_dict[ticker] = obter_dados_acao(ticker)
            else:
                print(f"⚠️  Ação {ticker} não encontrada na lista")
        
        criar_grafico_comparativo(acoes_selecionadas, dados_dict)
        
    elif opcao == "2":
        # Gráficos individuais
        entrada = input("Digite os códigos das ações separados por vírgula (ex: PETR4,VALE3): ")
        acoes_selecionadas = [f"{ticker.strip().upper()}.SA" for ticker in entrada.split(",")]
        
        for ticker in acoes_selecionadas:
            if ticker in ACOES_BRASILEIRAS:
                print(f"\n📊 Processando {ticker}...")
                dados = obter_dados_acao(ticker)
                if dados is not None:
                    fig = criar_grafico_individual(ticker, ACOES_BRASILEIRAS[ticker], dados)
                    plt.show()
            else:
                print(f"⚠️  Ação {ticker} não encontrada na lista")
                
    elif opcao == "3":
        # Ranking de performance
        print("\n📈 Obtendo dados de todas as ações para ranking...")
        dados_dict = {}
        total_acoes = len(ACOES_BRASILEIRAS)
        
        for i, ticker in enumerate(ACOES_BRASILEIRAS.keys(), 1):
            print(f"Processando {i}/{total_acoes}: {ticker.replace('.SA', '')}", end='\r')
            dados_dict[ticker] = obter_dados_acao(ticker)
        
        print("\n")
        exibir_ranking_performance(dados_dict)
        
    elif opcao == "4":
        # Análise completa
        print("\n🚀 Executando análise completa...")
        
        # Primeiro, obter dados de todas as ações
        print("📈 Obtendo dados de todas as ações...")
        dados_dict = {}
        total_acoes = len(ACOES_BRASILEIRAS)
        
        for i, ticker in enumerate(ACOES_BRASILEIRAS.keys(), 1):
            print(f"Processando {i}/{total_acoes}: {ticker.replace('.SA', '')}", end='\r')
            dados_dict[ticker] = obter_dados_acao(ticker)
        
        print("\n")
        
        # Exibir ranking
        exibir_ranking_performance(dados_dict)
        
        # Gráfico comparativo das top 10
        acoes_com_dados = [(ticker, dados) for ticker, dados in dados_dict.items() 
                          if dados is not None and not dados.empty]
        
        if acoes_com_dados:
            # Calcular performance para ordenar
            performances = []
            for ticker, dados in acoes_com_dados:
                preco_inicial = dados['Close'].iloc[0]
                preco_final = dados['Close'].iloc[-1]
                performance = ((preco_final / preco_inicial) - 1) * 100
                performances.append((ticker, performance))
            
            performances.sort(key=lambda x: x[1], reverse=True)
            top_10 = [ticker for ticker, _ in performances[:10]]
            
            print(f"\n📊 Criando gráfico comparativo das TOP 10...")
            criar_grafico_comparativo(top_10, dados_dict)
    
    else:
        print("❌ Opção inválida!")

if __name__ == "__main__":
    main()
